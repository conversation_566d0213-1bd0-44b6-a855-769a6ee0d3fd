import React, { useState } from 'react';
import { universalFileApi } from '@/lib/universal-file-api';

export function DebugFileUpload() {
  const [result, setResult] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('=== DEBUG FILE UPLOAD START ===');
    console.log('Selected file:', file);
    console.log('File properties:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    });

    setError('');
    setResult('Processing...');

    try {
      // Test environment detection
      console.log('Environment check:');
      console.log('- isWebEnvironment:', universalFileApi.isWebEnvironment());
      console.log('- isTauriEnvironment:', universalFileApi.isTauriEnvironment());

      // Test file validation
      console.log('Testing file validation...');
      const isValid = await universalFileApi.validateFile(file);
      console.log('File validation result:', isValid);

      if (!isValid) {
        throw new Error('File validation failed');
      }

      // Test file info
      console.log('Testing getFileInfo...');
      const fileInfo = await universalFileApi.getFileInfo(file);
      console.log('File info result:', fileInfo);

      setResult(JSON.stringify(fileInfo, null, 2));
      console.log('=== DEBUG FILE UPLOAD SUCCESS ===');
    } catch (err) {
      console.error('=== DEBUG FILE UPLOAD ERROR ===');
      console.error('Error:', err);
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMsg);
      setResult('');
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>Debug File Upload</h3>
      <input type="file" onChange={handleFileChange} />
      
      {error && (
        <div style={{ color: 'red', marginTop: '10px' }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {result && (
        <div style={{ marginTop: '10px' }}>
          <strong>Result:</strong>
          <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
            {result}
          </pre>
        </div>
      )}
    </div>
  );
}
